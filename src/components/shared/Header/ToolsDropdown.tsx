'use client';

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { DropdownLink } from './DropdownLink';
import { TOOLS_LINKS, CurrentPage } from './constants';

interface ToolsDropdownProps {
  currentPage?: CurrentPage;
  isMobile?: boolean;
  onClose?: () => void;
}

export const ToolsDropdown: React.FC<ToolsDropdownProps> = ({ 
  currentPage, 
  isMobile = false,
  onClose 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPos, setMenuPos] = useState<{ top: number; left: number } | null>(null);
  const [mounted, setMounted] = useState(false);

  // Imposta mounted a true dopo il montaggio del componente
  useEffect(() => {
    setMounted(true);
  }, []);

  // <PERSON>udi il dropdown quando si clicca fuori (solo per desktop)
  useEffect(() => {
    if (!isOpen || isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (buttonRef.current && buttonRef.current.contains(target)) return;
      if (menuRef.current && menuRef.current.contains(target)) return;
      setIsOpen(false);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isMobile]);

  // Aggiorna continuamente la posizione quando il menu è aperto (solo per desktop)
  useEffect(() => {
    if (!isOpen || isMobile) return;

    const updatePosition = () => {
      if (!buttonRef.current) return;
      const r = buttonRef.current.getBoundingClientRect();
      const menuWidth = 192; // w-48
      const gap = 4; // mt-1
      const padding = 8; // margine dai bordi viewport
      const left = Math.max(
        padding,
        Math.min(window.innerWidth - menuWidth - padding, r.left)
      );
      const top = r.bottom + gap;
      setMenuPos((prev) => (prev && prev.top === top && prev.left === left) ? prev : { top, left });
    };

    let frame = 0;
    const loop = () => {
      updatePosition();
      frame = requestAnimationFrame(loop);
    };
    frame = requestAnimationFrame(loop);

    // Aggiorna anche su resize/orientation change/visualViewport
    window.addEventListener('resize', updatePosition);
    window.addEventListener('orientationchange', updatePosition);
    window.visualViewport?.addEventListener('resize', updatePosition);
    window.visualViewport?.addEventListener('scroll', updatePosition);

    return () => {
      cancelAnimationFrame(frame);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('orientationchange', updatePosition);
      window.visualViewport?.removeEventListener('resize', updatePosition);
      window.visualViewport?.removeEventListener('scroll', updatePosition);
    };
  }, [isOpen, isMobile]);

  const handleToggle = () => {
    if (!isMobile) {
      // Pre-calcola la posizione prima dell'apertura per evitare flash (solo desktop)
      const r = buttonRef.current?.getBoundingClientRect();
      if (r) {
        const menuWidth = 192; // w-48
        const gap = 4;
        const padding = 8;
        const left = Math.max(
          padding,
          Math.min(window.innerWidth - menuWidth - padding, r.left)
        );
        const top = r.bottom + gap;
        setMenuPos({ top, left });
      }
    }
    setIsOpen((prev) => !prev);
  };

  const handleLinkClick = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };

  if (isMobile) {
    // Versione mobile semplificata (inline)
    return (
      <div className="relative">
        <DropdownLink
          label="Tools"
          isActive={currentPage === "tools"}
          onClick={handleToggle}
          isMobile={true}
          isOpen={isOpen}
        />

        {isOpen && (
          <div className="mt-1 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
            <div className="py-1">
              {TOOLS_LINKS.map((tool) => (
                <a
                  key={tool.id}
                  href={tool.href}
                  className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                  onClick={handleLinkClick}
                >
                  {tool.label}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Versione desktop con portal
  return (
    <div className="relative inline-block">
      <DropdownLink
        ref={buttonRef}
        label="Tools"
        isActive={currentPage === "tools"}
        onClick={handleToggle}
        isMobile={false}
        isOpen={isOpen}
      />

      {isOpen && menuPos && mounted && createPortal(
        <div
          ref={menuRef}
          className="w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-[1000]"
          style={{ position: 'fixed', top: menuPos.top, left: menuPos.left }}
        >
          <div className="py-1">
            {TOOLS_LINKS.map((tool) => (
              <a
                key={tool.id}
                href={tool.href}
                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                onClick={handleLinkClick}
              >
                {tool.label}
              </a>
            ))}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};
